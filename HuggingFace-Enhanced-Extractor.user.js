// ==UserScript==
// @name         HuggingFace镜像链接提取器-增强版
// @namespace    http://tampermonkey.net/
// @version      1.3
// @description  在HuggingFace页面提取下载链接，精确提取文件大小，支持排序功能
// <AUTHOR> Assistant
// @match        https://huggingface.co/*
// @match        https://hf-mirror.com/*
// @icon         https://www.google.com/s2/favicons?sz=64&domain=huggingface.co
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 专门针对HuggingFace页面的文件大小提取
    function extractFileSizeFromHF(element) {
        let sizeText = '';
        
        console.log('🔍 开始提取文件大小，元素:', element);
        
        // 方法1: 查找HuggingFace特定的文件列表结构
        // 通常文件大小在同一行的右侧
        const parentContainer = element.closest('li') || 
                               element.closest('tr') || 
                               element.closest('[class*="file"]') ||
                               element.closest('div');
        
        if (parentContainer) {
            console.log('📁 找到父容器:', parentContainer);
            
            // 查找所有可能包含文件大小的元素
            const allTextElements = parentContainer.querySelectorAll('*');
            
            for (const el of allTextElements) {
                const text = el.textContent.trim();
                console.log('🔍 检查文本:', text);
                
                // 匹配文件大小格式: 数字 + 单位 (支持 Bytes, kB, MB, GB 等)
                const sizeMatch = text.match(/^\s*(\d+(?:\.\d+)?)\s*(Bytes?|kB|KB|MB|GB)\s*$/i);
                if (sizeMatch) {
                    sizeText = sizeMatch[0].trim();
                    console.log('✅ 找到文件大小:', sizeText);
                    break;
                }
                
                // 也匹配包含文件大小的较长文本
                const sizeInText = text.match(/(\d+(?:\.\d+)?)\s*(Bytes?|kB|KB|MB|GB)\b/i);
                if (sizeInText && !sizeText) {
                    sizeText = sizeInText[0].trim();
                    console.log('✅ 在文本中找到文件大小:', sizeText);
                }
            }
        }
        
        // 方法2: 查找相邻元素
        if (!sizeText && element.parentElement) {
            const siblings = Array.from(element.parentElement.children);
            console.log('🔍 检查兄弟元素数量:', siblings.length);
            
            for (const sibling of siblings) {
                const text = sibling.textContent.trim();
                const sizeMatch = text.match(/(\d+(?:\.\d+)?)\s*(Bytes?|kB|KB|MB|GB)\b/i);
                if (sizeMatch) {
                    sizeText = sizeMatch[0].trim();
                    console.log('✅ 在兄弟元素中找到文件大小:', sizeText);
                    break;
                }
            }
        }
        
        // 方法3: 向上查找更大的容器
        if (!sizeText) {
            let currentElement = element;
            for (let i = 0; i < 5; i++) { // 最多向上查找5层
                currentElement = currentElement.parentElement;
                if (!currentElement) break;
                
                const text = currentElement.textContent;
                const sizeMatch = text.match(/(\d+(?:\.\d+)?)\s*(Bytes?|kB|KB|MB|GB)\b/i);
                if (sizeMatch) {
                    // 确保这个大小信息是属于当前文件的
                    const fileName = element.getAttribute('download') || element.textContent.trim();
                    if (text.includes(fileName)) {
                        sizeText = sizeMatch[0].trim();
                        console.log('✅ 在上级容器中找到文件大小:', sizeText);
                        break;
                    }
                }
            }
        }
        
        // 方法4: 全局搜索（最后的手段）
        if (!sizeText) {
            const fileName = element.getAttribute('download') || element.textContent.trim();
            if (fileName) {
                console.log('🔍 全局搜索文件:', fileName);
                
                // 查找页面中所有包含该文件名和大小信息的元素
                const allElements = document.querySelectorAll('*');
                for (const el of allElements) {
                    const text = el.textContent.trim();
                    if (text.includes(fileName)) {
                        const sizeMatch = text.match(/(\d+(?:\.\d+)?)\s*(Bytes?|kB|KB|MB|GB)\b/i);
                        if (sizeMatch) {
                            sizeText = sizeMatch[0].trim();
                            console.log('✅ 全局搜索找到文件大小:', sizeText);
                            break;
                        }
                    }
                }
            }
        }
        
        const result = sizeText || '未知';
        console.log('📊 最终文件大小结果:', result);
        return result;
    }

    // 解析文件大小为字节数
    function parseSizeToBytes(sizeStr) {
        if (!sizeStr || sizeStr === '未知') return 0;
        
        const match = sizeStr.match(/(\d+(?:\.\d+)?)\s*(Bytes?|kB|KB|MB|GB)\b/i);
        if (!match) return 0;
        
        const value = parseFloat(match[1]);
        const unit = match[2].toUpperCase();
        
        const multipliers = {
            'BYTE': 1, 'BYTES': 1, 'B': 1,
            'KB': 1024, 'kB': 1024,
            'MB': 1024 * 1024,
            'GB': 1024 * 1024 * 1024
        };
        
        return value * (multipliers[unit] || 1);
    }

    // 格式化文件大小显示
    function formatFileSize(bytes) {
        if (!bytes || bytes === 0) return '未知';
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    // 测试函数 - 在控制台中运行
    function testFileSizeExtraction() {
        console.log('🧪 开始测试文件大小提取...');
        const downloadLinks = document.querySelectorAll('a[download][href]');
        console.log(`找到 ${downloadLinks.length} 个下载链接`);
        
        downloadLinks.forEach((link, index) => {
            const fileName = link.getAttribute('download') || link.textContent.trim();
            const fileSize = extractFileSizeFromHF(link);
            console.log(`${index + 1}. ${fileName} -> ${fileSize}`);
        });
    }

    // 将测试函数暴露到全局，方便调试
    window.testHFFileSizeExtraction = testFileSizeExtraction;

    console.log('🚀 HuggingFace增强版提取器已加载');
    console.log('💡 在控制台运行 testHFFileSizeExtraction() 来测试文件大小提取');
})();
